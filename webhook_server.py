#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp Webhook Server для интеграции с Microsoft Access
Продакшн-версия с полной обработкой ошибок и логированием
"""

import os
import json
import logging
from logging.handlers import RotatingFileHandler
import pyodbc
import requests
from datetime import datetime
from flask import Flask, request, jsonify
from dotenv import load_dotenv
import hashlib
import hmac
import time
import sys
import flask
from typing import Dict, Any, Optional

# Загрузка переменных окружения
load_dotenv()

# Включаем пул соединений для pyodbc
pyodbc.pooling = True

# Время запуска сервера
start_time = time.time()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('whatsapp_webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
# Добавляем ротацию логов
rot_handler = RotatingFileHandler('whatsapp_webhook.log', maxBytes=10*1024*1024, backupCount=5)
rot_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(rot_handler)

app = Flask(__name__)
# Ограничиваем размер тела запроса до 2 МБ
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024

class WhatsAppDatabase:
    """Класс для работы с SQL Server Express через ODBC"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.connection = None
        self.cursor = None
        self._request_id = None
    
    def connect(self) -> bool:
        """Подключение к базе данных SQL Server Express"""
        try:
            # Настройки подключения для SQL Server Express
            self.connection = pyodbc.connect(
                self.connection_string,
                timeout=30,  # Таймаут подключения
                autocommit=False  # Явное управление транзакциями
            )
            self.cursor = self.connection.cursor()
            
            # Проверка подключения
            self.cursor.execute("SELECT 1")
            self.cursor.fetchone()
            
            logger.info("Успешное подключение к SQL Server Express")
            return True
        except Exception as e:
            logger.error(f"Ошибка подключения к SQL Server Express: {e}")
            return False
    
    def disconnect(self):
        """Отключение от базы данных"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
        except Exception as e:
            logger.error(f"Ошибка при отключении от БД: {e}")
    
    def generate_request_id(self) -> str:
        """Генерация уникального ID запроса для трассировки"""
        import uuid
        self._request_id = str(uuid.uuid4())[:8]
        return self._request_id
    
    def log_message(self, level: str, message: str, source: str = "webhook", 
                   user_id: str = None, exception_details: str = None):
        """Запись лога в базу данных с расширенной информацией"""
        try:
            request_id = self._request_id or self.generate_request_id()
            
            self.cursor.execute("""
                INSERT INTO Logs (LogLevel, Message, Source, UserId, RequestId, ExceptionDetails)
                VALUES (?, ?, ?, ?, ?, ?)
            """, level, message, source, user_id, request_id, exception_details)
            self.connection.commit()
        except Exception as e:
            logger.error(f"Ошибка записи лога в БД: {e}")
    
    def save_message(self, message_data: Dict[str, Any]) -> bool:
        """Сохранение входящего сообщения в базу с улучшенной обработкой"""
        try:
            self.generate_request_id()
            
            # Извлечение данных из webhook
            message_id = message_data.get('id')
            chat_id = message_data.get('from')
            from_user = message_data.get('from')
            to_user = message_data.get('to', '')
            
            # Обработка текста сообщения
            text = ''
            if message_data.get('text'):
                text = message_data['text'].get('body', '')
            elif message_data.get('interactive'):
                # Обработка интерактивных сообщений (кнопки, списки)
                interactive = message_data['interactive']
                if interactive.get('button_reply'):
                    text = interactive['button_reply'].get('title', '')
                elif interactive.get('list_reply'):
                    text = interactive['list_reply'].get('title', '')
            
            message_type = message_data.get('type', 'text')
            
            # Обработка времени
            timestamp_str = message_data.get('timestamp', str(int(time.time())))
            timestamp = datetime.fromtimestamp(int(timestamp_str))
            
            # Обработка медиафайлов
            media_url = None
            if message_type in ['image', 'document', 'audio', 'video']:
                media_data = message_data.get(message_type, {})
                media_url = media_data.get('url', '')
            
            # Проверка на дублирование сообщений
            self.cursor.execute("SELECT Id FROM Messages WHERE MessageID = ?", message_id)
            if self.cursor.fetchone():
                logger.warning(f"Сообщение {message_id} уже существует в базе")
                return True
            
            # Сохранение сообщения
            self.cursor.execute("""
                INSERT INTO Messages (MessageID, ChatID, FromUser, ToUser, Text, MessageType, 
                                    MediaURL, SentAt, Direction, RawData)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'incoming', ?)
            """, message_id, chat_id, from_user, to_user, text, message_type, 
                media_url, timestamp, json.dumps(message_data, ensure_ascii=False))
            
            # Обновление статистики контакта
            self.update_contact_stats(from_user, timestamp)
            
            self.connection.commit()
            
            self.log_message("INFO", f"Сообщение {message_id} сохранено в базу", 
                           "webhook", from_user)
            logger.info(f"Сообщение {message_id} от {from_user} сохранено в базу")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка сохранения сообщения: {e}")
            self.log_message("ERROR", f"Ошибка сохранения сообщения: {str(e)}", 
                           "webhook", exception_details=str(e))
            if self.connection:
                self.connection.rollback()
            return False
    
    def update_contact_stats(self, phone_number: str, last_message_time: datetime):
        """Обновление статистики контакта с улучшенной логикой"""
        try:
            # Проверяем, существует ли контакт
            self.cursor.execute("SELECT Id FROM Contacts WHERE PhoneNumber = ?", phone_number)
            contact = self.cursor.fetchone()
            
            if not contact:
                # Создаем новый контакт
                contact_name = f"Контакт {phone_number[-4:]}"  # Последние 4 цифры
                self.cursor.execute("""
                    INSERT INTO Contacts (PhoneNumber, ContactName, CreatedAt, UpdatedAt, 
                                        LastMessageAt, MessageCount, IsActive)
                    VALUES (?, ?, ?, ?, ?, 1, 1)
                """, phone_number, contact_name, datetime.utcnow(), datetime.utcnow(), last_message_time)
                
                self.log_message("INFO", f"Создан новый контакт: {phone_number}", "contact_manager")
            else:
                # Обновляем существующий контакт
                self.cursor.execute("""
                    UPDATE Contacts 
                    SET LastMessageAt = ?, MessageCount = MessageCount + 1, 
                        UpdatedAt = ?, IsActive = 1
                    WHERE PhoneNumber = ?
                """, last_message_time, datetime.utcnow(), phone_number)
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"Ошибка обновления статистики контакта: {e}")
            self.log_message("ERROR", f"Ошибка обновления контакта {phone_number}: {str(e)}", 
                           "contact_manager", exception_details=str(e))
    
    def get_setting(self, key: str) -> Optional[str]:
        """Получение настройки из базы"""
        try:
            self.cursor.execute("SELECT SettingValue FROM Settings WHERE SettingKey = ?", key)
            result = self.cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Ошибка получения настройки {key}: {e}")
            return None
    
    def save_outgoing_message(self, to_user: str, message_text: str, 
                            message_type: str = 'text', priority: int = 1) -> int:
        """Сохранение исходящего сообщения в очередь"""
        try:
            self.cursor.execute("""
                INSERT INTO OutgoingMessages (ToUser, MessageText, MessageType, Priority, CreatedAt)
                OUTPUT INSERTED.Id
                VALUES (?, ?, ?, ?, ?)
            """, to_user, message_text, message_type, priority, datetime.utcnow())
            
            result = self.cursor.fetchone()
            message_id = result[0] if result else None
            
            self.connection.commit()
            
            self.log_message("INFO", f"Исходящее сообщение добавлено в очередь: ID {message_id}", 
                           "outgoing_queue", to_user)
            return message_id
            
        except Exception as e:
            logger.error(f"Ошибка сохранения исходящего сообщения: {e}")
            self.log_message("ERROR", f"Ошибка сохранения исходящего сообщения: {str(e)}", 
                           "outgoing_queue", exception_details=str(e))
            return None
    
    def update_outgoing_message_status(self, message_id: int, status: str, 
                                     whatsapp_message_id: str = None, 
                                     error_message: str = None):
        """Обновление статуса исходящего сообщения"""
        try:
            sent_at = datetime.utcnow() if status == 'sent' else None
            
            self.cursor.execute("""
                UPDATE OutgoingMessages 
                SET Status = ?, SentAt = ?, WhatsAppMessageID = ?, ErrorMessage = ?
                WHERE Id = ?
            """, status, sent_at, whatsapp_message_id, error_message, message_id)
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"Ошибка обновления статуса сообщения {message_id}: {e}")
    
    def cleanup_old_logs(self, retention_days: int = 30):
        """Очистка старых логов"""
        try:
            self.cursor.execute("EXEC sp_CleanupOldLogs ?", retention_days)
            result = self.cursor.fetchone()
            deleted_count = result[0] if result else 0
            
            self.connection.commit()
            logger.info(f"Удалено {deleted_count} старых записей логов")
            
        except Exception as e:
            logger.error(f"Ошибка очистки логов: {e}")
    
    def update_all_contact_stats(self):
        """Обновление статистики всех контактов"""
        try:
            self.cursor.execute("EXEC sp_UpdateContactStats")
            result = self.cursor.fetchone()
            updated_count = result[0] if result else 0
            
            self.connection.commit()
            logger.info(f"Обновлена статистика {updated_count} контактов")
            
        except Exception as e:
            logger.error(f"Ошибка обновления статистики контактов: {e}")

class WhatsAppAPI:
    """Класс для работы с WhatsApp Cloud API с интеграцией SQL Server"""
    
    def __init__(self, phone_number_id: str, access_token: str, database: WhatsAppDatabase = None):
        self.phone_number_id = phone_number_id
        self.access_token = access_token
        self.database = database
        self.base_url = "https://graph.facebook.com/v18.0"
        
        # Настройки для rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Минимальный интервал между запросами (секунды)
    
    def _rate_limit(self):
        """Контроль частоты запросов"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _log_api_call(self, method: str, to: str, payload: dict, response: dict):
        """Логирование API вызовов"""
        if self.database:
            log_message = f"WhatsApp API {method} to {to}: {response.get('success', False)}"
            self.database.log_message("INFO", log_message, "whatsapp_api", to)
    
    def send_message(self, to_number: str, message_text: str, save_to_db: bool = True) -> Dict[str, Any]:
        """Отправка сообщения через WhatsApp API с сохранением в БД"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/{self.phone_number_id}/messages"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            data = {
                "messaging_product": "whatsapp",
                "to": to_number,
                "type": "text",
                "text": {"body": message_text}
            }
            
            # Сохранение в очередь исходящих сообщений
            message_db_id = None
            if save_to_db and self.database:
                message_db_id = self.database.save_outgoing_message(to_number, message_text, "text")
            
            response = requests.post(url, headers=headers, json=data, timeout=(3.05, 10))
            response.raise_for_status()
            
            result = response.json()
            
            # Обновление статуса в БД
            if message_db_id and self.database:
                whatsapp_message_id = result.get('messages', [{}])[0].get('id')
                self.database.update_outgoing_message_status(
                    message_db_id, 'sent', whatsapp_message_id
                )
            
            self._log_api_call("send_message", to_number, data, {"success": True})
            logger.info(f"Сообщение отправлено: {result}")
            return result
            
        except Exception as e:
            error_msg = str(e)
            
            # Обновление статуса ошибки в БД
            if message_db_id and self.database:
                self.database.update_outgoing_message_status(
                    message_db_id, 'failed', error_message=error_msg
                )
            
            self._log_api_call("send_message", to_number, data, {"success": False, "error": error_msg})
            logger.error(f"Ошибка отправки сообщения: {e}")
            return {"error": error_msg}
    
    def send_template_message(self, to_number: str, template_name: str, language_code: str = "ru", 
                            parameters: list = None, save_to_db: bool = True) -> Dict[str, Any]:
        """Отправка шаблонного сообщения с параметрами"""
        self._rate_limit()
        
        template_data = {
            "name": template_name,
            "language": {"code": language_code}
        }
        
        # Добавление параметров шаблона
        if parameters:
            template_data["components"] = [{
                "type": "body",
                "parameters": [{"type": "text", "text": param} for param in parameters]
            }]
        
        data = {
            "messaging_product": "whatsapp",
            "to": to_number,
            "type": "template",
            "template": template_data
        }
        
        # Сохранение в очередь исходящих сообщений
        message_text = f"Template: {template_name}"
        if parameters:
            message_text += f" with params: {', '.join(parameters)}"
        
        message_db_id = None
        if save_to_db and self.database:
            message_db_id = self.database.save_outgoing_message(to_number, message_text, "template")
        
        try:
            url = f"{self.base_url}/{self.phone_number_id}/messages"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            # Обновление статуса в БД
            if message_db_id and self.database:
                whatsapp_message_id = result.get('messages', [{}])[0].get('id')
                self.database.update_outgoing_message_status(
                    message_db_id, 'sent', whatsapp_message_id
                )
            
            self._log_api_call("send_template", to_number, data, {"success": True})
            logger.info(f"Шаблонное сообщение {template_name} отправлено на {to_number}")
            
            return {"success": True, "data": result, "db_id": message_db_id}
            
        except Exception as e:
            error_msg = str(e)
            
            # Обновление статуса ошибки в БД
            if message_db_id and self.database:
                self.database.update_outgoing_message_status(
                    message_db_id, 'failed', error_message=error_msg
                )
            
            self._log_api_call("send_template", to_number, data, {"success": False, "error": error_msg})
            logger.error(f"Ошибка отправки шаблонного сообщения {template_name}: {e}")
            
            return {"success": False, "error": error_msg, "db_id": message_db_id}

# Инициализация объектов
db = WhatsAppDatabase(os.getenv('ACCESS_DSN', ''))
whatsapp_api = None

def initialize_whatsapp_api():
    """Инициализация WhatsApp API с настройками из базы"""
    global whatsapp_api
    if db.connect():
        phone_number_id = db.get_setting('whatsapp_phone_number_id')
        access_token = db.get_setting('whatsapp_access_token')
        
        if phone_number_id and access_token:
            whatsapp_api = WhatsAppAPI(phone_number_id, access_token, db)
            logger.info("WhatsApp API инициализирован")
        else:
            logger.warning("Не удалось получить настройки WhatsApp API из базы")
        db.disconnect()

@app.route('/webhook', methods=['GET', 'POST'])
def webhook():
    """Обработка webhook от WhatsApp с улучшенной логикой"""
    
    if request.method == 'GET':
        # Верификация webhook
        try:
            mode = request.args.get('hub.mode')
            token = request.args.get('hub.verify_token')
            challenge = request.args.get('hub.challenge')
            
            if db.connect():
                verify_token = db.get_setting('webhook_verify_token')
                db.disconnect()
            else:
                verify_token = os.getenv('WEBHOOK_VERIFY_TOKEN', '')
            
            if mode == 'subscribe' and token == verify_token:
                logger.info("Webhook верифицирован успешно")
                if db.connect():
                    db.log_message("INFO", "Webhook верифицирован успешно", "webhook_verification")
                    db.disconnect()
                return challenge
            else:
                logger.error(f"Неверный токен верификации webhook: {token}")
                if db.connect():
                    db.log_message("ERROR", f"Неверный токен верификации: {token}", "webhook_verification")
                    db.disconnect()
                return 'Forbidden', 403
                
        except Exception as e:
            logger.error(f"Ошибка верификации webhook: {e}")
            return 'Internal Server Error', 500
    
    elif request.method == 'POST':
        # Обработка входящих сообщений
        try:
            # Проверка подписи (опционально, но рекомендуется)
            if not verify_signature(request):
                logger.warning("Неверная подпись webhook")
                return 'Unauthorized', 401
            
            data = request.json
            
            # Проверка API-ключа
            required_key = os.getenv('ACCESS_API_KEY')
            if required_key and request.headers.get('X-Api-Key') != required_key:
                return jsonify({"error": "Unauthorized"}), 401
            
            # Проверка валидности данных
            if not data:
                logger.warning("Получены пустые данные webhook")
                return "Bad Request", 400
            
            logger.info(f"Получены данные webhook от {request.remote_addr}")
            
            if not db.connect():
                logger.error("Не удалось подключиться к базе данных")
                return 'Internal Server Error', 500
            
            # Логирование в базу данных
            db.log_message("INFO", f"Webhook получен от {request.remote_addr}", "webhook")
            
            # Обработка сообщений
            if data.get('object') == 'whatsapp_business_account':
                processed_messages = 0
                
                for entry in data.get('entry', []):
                    for change in entry.get('changes', []):
                        if change.get('field') == 'messages':
                            value = change.get('value', {})
                            
                            # Обработка статусов сообщений
                            for status in value.get('statuses', []):
                                message_id = status.get('id')
                                status_type = status.get('status')
                                recipient_id = status.get('recipient_id')
                                
                                logger.info(f"Статус сообщения {message_id}: {status_type}")
                                db.log_message("INFO", 
                                              f"Статус сообщения {message_id}: {status_type}", 
                                              "message_status", recipient_id)
                            
                            # Обработка входящих сообщений
                            for message in value.get('messages', []):
                                message_id = message.get('id')
                                from_number = message.get('from')
                                message_type = message.get('type', 'unknown')
                                
                                logger.info(f"Обработка сообщения {message_id} от {from_number} типа {message_type}")
                                
                                # Сохранение сообщения
                                if db.save_message(message):
                                    processed_messages += 1
                                    logger.info(f"Сообщение {message_id} сохранено")
                                    
                                    # Проверка автоответа
                                    auto_reply_enabled = db.get_setting('auto_reply_enabled')
                                    if auto_reply_enabled == '1' and whatsapp_api:
                                        # Проверка времени работы автоответа
                                        auto_reply_hours = db.get_setting('auto_reply_hours')
                                        current_hour = datetime.now().hour
                                        
                                        should_reply = True
                                        if auto_reply_hours:
                                            try:
                                                start_hour, end_hour = map(int, auto_reply_hours.split('-'))
                                                should_reply = start_hour <= current_hour <= end_hour
                                            except:
                                                pass
                                        
                                        if should_reply:
                                            reply_text = db.get_setting('auto_reply_message') or "Спасибо за ваше сообщение! Мы ответим вам в ближайшее время."
                                            
                                            # Отправка автоответа
                                            result = whatsapp_api.send_message(from_number, reply_text)
                                            if result.get('error'):
                                                logger.error(f"Ошибка отправки автоответа: {result['error']}")
                                            else:
                                                logger.info(f"Автоответ отправлен на {from_number}")
                                else:
                                    logger.error(f"Ошибка сохранения сообщения {message_id}")
                
                # Логирование результатов обработки
                if processed_messages > 0:
                    db.log_message("INFO", f"Обработано сообщений: {processed_messages}", "webhook")
            
            db.disconnect()
            return 'OK', 200
            
        except Exception as e:
            error_msg = f"Ошибка обработки webhook: {str(e)}"
            logger.error(error_msg)
            
            if db.connection:
                db.log_message("ERROR", error_msg, "webhook", exception_details=str(e))
                db.disconnect()
            
            return 'Internal Server Error', 500

def verify_signature(request) -> bool:
    """Проверка подписи webhook (для безопасности)"""
    try:
        signature = request.headers.get('X-Hub-Signature-256', '')
        if not signature:
            return True  # Пропускаем, если подпись не настроена
        
        app_secret = os.getenv('WHATSAPP_APP_SECRET', '')
        if not app_secret:
            return True  # Пропускаем, если секрет не настроен
        
        expected_signature = 'sha256=' + hmac.new(
            app_secret.encode('utf-8'),
            request.data,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)
        
    except Exception as e:
        logger.error(f"Ошибка проверки подписи: {e}")
        return False

def should_send_auto_reply() -> bool:
    """Проверка, нужно ли отправлять автоответ"""
    try:
        auto_reply_enabled = db.get_setting('auto_reply_enabled')
        return auto_reply_enabled == '1'
    except:
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Расширенная проверка состояния сервера и SQL Server"""
    health_info = {
        "status": "unknown",
        "timestamp": datetime.now().isoformat(),
        "server": {
            "python_version": sys.version,
            "flask_version": flask.__version__,
            "uptime": time.time() - start_time if 'start_time' in globals() else 0
        },
        "database": {
            "status": "unknown",
            "connection": False,
            "sql_server_info": None,
            "database_size": None,
            "table_stats": {}
        },
        "whatsapp_api": {
            "initialized": whatsapp_api is not None,
            "status": "unknown"
        }
    }
    
    try:
        # Проверка подключения к базе данных
        if db.connect():
            health_info["database"]["connection"] = True
            
            try:
                # Получение информации о SQL Server
                cursor = db.connection.cursor()
                
                # Версия SQL Server
                cursor.execute("SELECT @@VERSION")
                sql_version = cursor.fetchone()[0]
                health_info["database"]["sql_server_info"] = sql_version.split('\n')[0]
                
                # Размер базы данных
                cursor.execute("""
                    SELECT 
                        DB_NAME() as DatabaseName,
                        SUM(size * 8.0 / 1024) as SizeMB
                    FROM sys.database_files
                """)
                db_size = cursor.fetchone()
                if db_size:
                    health_info["database"]["database_size"] = f"{db_size[1]:.2f} MB"
                
                # Статистика таблиц
                tables = ['Messages', 'OutgoingMessages', 'Contacts', 'Settings', 'Logs']
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        health_info["database"]["table_stats"][table] = count
                    except:
                        health_info["database"]["table_stats"][table] = "error"
                
                health_info["database"]["status"] = "healthy"
                
            except Exception as e:
                health_info["database"]["status"] = f"connected_but_error: {str(e)}"
            
            db.disconnect()
        else:
            health_info["database"]["status"] = "connection_failed"
        
        # Проверка WhatsApp API
        if whatsapp_api:
            try:
                # Простая проверка доступности API (можно расширить)
                health_info["whatsapp_api"]["status"] = "initialized"
                health_info["whatsapp_api"]["phone_number_id"] = whatsapp_api.phone_number_id
                health_info["whatsapp_api"]["access_token"] = "configured" if whatsapp_api.access_token else "missing"
            except Exception as e:
                health_info["whatsapp_api"]["status"] = f"error: {str(e)}"
        else:
            health_info["whatsapp_api"]["status"] = "not_initialized"
        
        # Общий статус
        if (health_info["database"]["connection"] and 
            health_info["database"]["status"] == "healthy" and
            health_info["whatsapp_api"]["initialized"]):
            health_info["status"] = "healthy"
            return jsonify(health_info), 200
        else:
            health_info["status"] = "unhealthy"
            return jsonify(health_info), 503
            
    except Exception as e:
        health_info["status"] = "error"
        health_info["error"] = str(e)
        return jsonify(health_info), 500

@app.route('/send', methods=['POST'])
def send_message():
    """API для отправки сообщений и шаблонов из Access"""
    try:
        data = request.json
        to_number = data.get('to')
        message_type = data.get('type', 'text')  # text или template
        
        if not to_number:
            return jsonify({"error": "Missing 'to' field"}), 400
        
        if not whatsapp_api:
            return jsonify({"error": "WhatsApp API not initialized"}), 500
        
        result = None
        
        if message_type == 'text':
            # Отправка текстового сообщения
            message_text = data.get('message')
            if not message_text:
                return jsonify({"error": "Missing 'message' field for text message"}), 400
            
            result = whatsapp_api.send_text_message(to_number, message_text)
            
        elif message_type == 'template':
            # Отправка шаблонного сообщения
            template_name = data.get('template_name')
            language_code = data.get('language_code', 'ru')
            parameters = data.get('parameters', [])
            
            if not template_name:
                return jsonify({"error": "Missing 'template_name' field for template message"}), 400
            
            result = whatsapp_api.send_template_message(
                to_number, 
                template_name, 
                language_code, 
                parameters
            )
            
        else:
            return jsonify({"error": f"Unsupported message type: {message_type}"}), 400
        
        if result and 'error' in result:
            return jsonify(result), 500
        else:
            return jsonify({
                "status": "sent", 
                "message_type": message_type,
                "result": result
            })
            
    except Exception as e:
        logger.error(f"Ошибка отправки сообщения: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/stats', methods=['GET'])
def get_statistics():
    """Получение статистики из SQL Server"""
    try:
        if not db.connect():
            return jsonify({"error": "Database connection failed"}), 500
        
        stats = {
            "timestamp": datetime.now().isoformat(),
            "messages": {},
            "contacts": {},
            "outgoing_messages": {},
            "logs": {},
            "recent_activity": []
        }
        
        cursor = db.connection.cursor()
        
        # Статистика сообщений
        cursor.execute("""
            SELECT 
                Direction,
                COUNT(*) as Count,
                MAX(SentAt) as LastMessage
            FROM Messages 
            GROUP BY Direction
        """)
        for row in cursor.fetchall():
            stats["messages"][row[0]] = {
                "count": row[1],
                "last_message": row[2].isoformat() if row[2] else None
            }
        
        # Статистика контактов
        cursor.execute("""
            SELECT 
                COUNT(*) as TotalContacts,
                COUNT(CASE WHEN IsBlocked = 1 THEN 1 END) as BlockedContacts,
                COUNT(CASE WHEN LastMessageAt > DATEADD(day, -7, GETDATE()) THEN 1 END) as ActiveLastWeek
            FROM Contacts
        """)
        contact_stats = cursor.fetchone()
        if contact_stats:
            stats["contacts"] = {
                "total": contact_stats[0],
                "blocked": contact_stats[1],
                "active_last_week": contact_stats[2]
            }
        
        # Статистика исходящих сообщений
        cursor.execute("""
            SELECT 
                Status,
                COUNT(*) as Count
            FROM OutgoingMessages 
            WHERE CreatedAt > DATEADD(day, -7, GETDATE())
            GROUP BY Status
        """)
        for row in cursor.fetchall():
            stats["outgoing_messages"][row[0]] = row[1]
        
        # Статистика логов
        cursor.execute("""
            SELECT 
                Source,
                COUNT(*) as Count
            FROM Logs 
            WHERE CreatedAt > DATEADD(day, -1, GETDATE())
            GROUP BY Source
        """)
        for row in cursor.fetchall():
            stats["logs"][row[0]] = row[1]
        
        # Последняя активность
        cursor.execute("""
            SELECT TOP 10
                Source,
                Message,
                CreatedAt
            FROM Logs 
            ORDER BY CreatedAt DESC
        """)
        for row in cursor.fetchall():
            stats["recent_activity"].append({
                "source": row[0],
                "message": row[1],
                "timestamp": row[2].isoformat() if row[2] else None
            })
        
        db.disconnect()
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Ошибка получения статистики: {e}")
        if db.connection:
            db.disconnect()
        return jsonify({"error": str(e)}), 500

@app.route('/maintenance', methods=['POST'])
def run_maintenance():
    """Запуск задач обслуживания базы данных"""
    try:
        data = request.json or {}
        task = data.get('task', 'all')
        
        if not db.connect():
            return jsonify({"error": "Database connection failed"}), 500
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "tasks_completed": [],
            "errors": []
        }
        
        cursor = db.connection.cursor()
        
        if task in ['all', 'cleanup_logs']:
            try:
                # Очистка старых логов
                retention_days = int(db.get_setting('log_retention_days') or '30')
                cursor.execute("EXEC sp_CleanupOldLogs ?", (retention_days,))
                db.connection.commit()
                results["tasks_completed"].append(f"cleanup_logs (retention: {retention_days} days)")
            except Exception as e:
                results["errors"].append(f"cleanup_logs: {str(e)}")
        
        if task in ['all', 'update_contact_stats']:
            try:
                # Обновление статистики контактов
                cursor.execute("EXEC sp_UpdateContactStats")
                db.connection.commit()
                results["tasks_completed"].append("update_contact_stats")
            except Exception as e:
                results["errors"].append(f"update_contact_stats: {str(e)}")
        
        if task in ['all', 'cleanup_old_messages']:
            try:
                # Очистка старых сообщений (опционально)
                message_retention_days = data.get('message_retention_days', 365)
                cursor.execute("""
                    DELETE FROM Messages 
                    WHERE SentAt < DATEADD(day, -?, GETDATE())
                """, (message_retention_days,))
                deleted_count = cursor.rowcount
                db.connection.commit()
                results["tasks_completed"].append(f"cleanup_old_messages (deleted: {deleted_count})")
            except Exception as e:
                results["errors"].append(f"cleanup_old_messages: {str(e)}")
        
        if task in ['all', 'update_indexes']:
            try:
                # Обновление статистики индексов
                cursor.execute("UPDATE STATISTICS Messages")
                cursor.execute("UPDATE STATISTICS OutgoingMessages")
                cursor.execute("UPDATE STATISTICS Contacts")
                cursor.execute("UPDATE STATISTICS Logs")
                db.connection.commit()
                results["tasks_completed"].append("update_indexes")
            except Exception as e:
                results["errors"].append(f"update_indexes: {str(e)}")
        
        db.disconnect()
        
        # Логирование результатов
        if db.connect():
            db.log_message("INFO", f"Maintenance completed: {len(results['tasks_completed'])} tasks, {len(results['errors'])} errors", "maintenance")
            db.disconnect()
        
        status_code = 200 if not results["errors"] else 207  # 207 Multi-Status
        return jsonify(results), status_code
        
    except Exception as e:
        logger.error(f"Ошибка обслуживания: {e}")
        if db.connection:
            db.disconnect()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    # Инициализация при запуске
    initialize_whatsapp_api()
    
    # Запуск сервера
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Запуск WhatsApp Webhook Server на порту {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)