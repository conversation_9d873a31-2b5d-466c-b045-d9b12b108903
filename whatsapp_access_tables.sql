-- =====================================================
-- SQL скрипты для интеграции WhatsApp с SQL Server Express
-- Версия: 2.0 (оптимизировано для SQL Server Express)
-- =====================================================

-- Основная таблица для хранения входящих сообщений WhatsApp
CREATE TABLE Messages (
    Id INT IDENTITY(1,1) PRIMARY KEY,    -- Автоинкрементный первичный ключ
    MessageID VARCHAR(100) UNIQUE NOT NULL, -- Уникальный ID сообщения от WhatsApp
    ChatID VARCHAR(50) NOT NULL,         -- ID чата/диалога
    FromUser VARCHAR(100) NOT NULL,      -- Номер отправителя
    ToUser VARCHAR(100),                 -- Номер получателя (ваш номер)
    Text NVARCHAR(MAX),                  -- Текст сообщения (поддержка Unicode)
    MessageType VARCHAR(50) DEFAULT 'text', -- Тип сообщения (text, image, document, etc.)
    MediaURL VARCHAR(500),               -- URL медиафайла (если есть)
    SentAt DATETIME NOT NULL,            -- Время отправки сообщения
    ReceivedAt DATETIME DEFAULT GETDATE(), -- Время получения на сервере
    Direction VARCHAR(10) DEFAULT 'incoming', -- incoming / outgoing
    Status VARCHAR(50) DEFAULT 'received', -- Статус обработки
    RawData NVARCHAR(MAX)                -- Полные данные от WhatsApp API (JSON)
);

-- Таблица для отправки сообщений из Access
CREATE TABLE OutgoingMessages (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ToUser VARCHAR(100) NOT NULL,        -- Номер получателя
    MessageText NVARCHAR(MAX) NOT NULL,  -- Текст сообщения (поддержка Unicode)
    MessageType VARCHAR(50) DEFAULT 'text', -- Тип сообщения
    MediaPath VARCHAR(500),              -- Путь к медиафайлу (если есть)
    Priority INT DEFAULT 1,              -- Приоритет отправки (1-5)
    ScheduledAt DATETIME,                -- Время запланированной отправки
    CreatedAt DATETIME DEFAULT GETDATE(), -- Время создания записи
    SentAt DATETIME,                     -- Время фактической отправки
    Status VARCHAR(50) DEFAULT 'pending', -- Статус: pending, sent, failed, cancelled
    WhatsAppMessageID VARCHAR(100),      -- ID сообщения в WhatsApp (после отправки)
    ErrorMessage NVARCHAR(MAX),          -- Сообщение об ошибке (если есть)
    RetryCount INT DEFAULT 0             -- Количество попыток отправки
);

-- Таблица для хранения информации о контактах
CREATE TABLE Contacts (
    Id INT IDENTITY(1,1) PRIMARY KEY,    -- Автоинкрементный ключ
    PhoneNumber VARCHAR(100) UNIQUE NOT NULL, -- Номер телефона
    ContactName NVARCHAR(255),           -- Имя контакта (поддержка Unicode)
    Company NVARCHAR(255),               -- Компания
    Email VARCHAR(255),                  -- Email
    Notes NVARCHAR(MAX),                 -- Заметки
    Tags NVARCHAR(500),                  -- Теги через запятую
    CreatedAt DATETIME DEFAULT GETDATE(), -- Дата создания записи
    UpdatedAt DATETIME DEFAULT GETDATE(), -- Дата последнего обновления
    LastMessageAt DATETIME,              -- Время последнего сообщения
    MessageCount INT DEFAULT 0,          -- Количество сообщений
    IsActive BIT DEFAULT 1,              -- Активен ли контакт
    IsBlocked BIT DEFAULT 0              -- Заблокирован ли контакт
);

-- Таблица для хранения настроек интеграции
CREATE TABLE Settings (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    SettingKey VARCHAR(100) UNIQUE NOT NULL,
    SettingValue NVARCHAR(MAX) NOT NULL,
    Description NVARCHAR(500),
    Category VARCHAR(50) DEFAULT 'general', -- Категория настройки
    IsEncrypted BIT DEFAULT 0,           -- Зашифрована ли настройка
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE()
);

-- Таблица для логирования операций
CREATE TABLE Logs (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    LogLevel VARCHAR(20) NOT NULL,       -- DEBUG, INFO, WARNING, ERROR, CRITICAL
    Message NVARCHAR(MAX) NOT NULL,      -- Текст сообщения
    Source VARCHAR(100),                 -- Источник (webhook, sender, api, etc.)
    UserId VARCHAR(100),                 -- ID пользователя (если применимо)
    RequestId VARCHAR(50),               -- ID запроса для трассировки
    ExceptionDetails NVARCHAR(MAX),      -- Детали исключения
    CreatedAt DATETIME DEFAULT GETDATE() -- Время создания записи
);

-- =====================================================
-- Вставка базовых настроек
-- =====================================================
INSERT INTO Settings (SettingKey, SettingValue, Description, Category) VALUES
('whatsapp_phone_number_id', '', 'Phone Number ID из Meta Business', 'whatsapp'),
('whatsapp_business_account_id', '', 'Business Account ID из Meta', 'whatsapp'),
('whatsapp_access_token', '', 'Access Token для WhatsApp API', 'whatsapp'),
('webhook_verify_token', '', 'Токен для верификации webhook', 'security'),
('whatsapp_app_secret', '', 'App Secret для проверки подписи', 'security'),
('auto_reply_enabled', '0', 'Включить автоответы (0/1)', 'automation'),
('auto_reply_message', 'Спасибо за сообщение! Мы ответим в ближайшее время.', 'Текст автоответа', 'automation'),
('max_message_length', '4096', 'Максимальная длина сообщения', 'limits'),
('rate_limit_per_minute', '60', 'Лимит сообщений в минуту', 'limits'),
('webhook_timeout_seconds', '30', 'Таймаут webhook в секундах', 'performance'),
('log_retention_days', '30', 'Количество дней хранения логов', 'maintenance'),
('enable_message_encryption', '0', 'Включить шифрование сообщений', 'security');

-- =====================================================
-- Индексы для оптимизации производительности
-- =====================================================
-- Индексы для таблицы Messages
CREATE INDEX IX_Messages_ChatID ON Messages(ChatID);
CREATE INDEX IX_Messages_FromUser ON Messages(FromUser);
CREATE INDEX IX_Messages_SentAt ON Messages(SentAt DESC);
CREATE INDEX IX_Messages_Status ON Messages(Status);
CREATE INDEX IX_Messages_Direction ON Messages(Direction);
CREATE INDEX IX_Messages_MessageType ON Messages(MessageType);
CREATE INDEX IX_Messages_ReceivedAt ON Messages(ReceivedAt DESC);

-- Составные индексы для частых запросов
CREATE INDEX IX_Messages_FromUser_SentAt ON Messages(FromUser, SentAt DESC);
CREATE INDEX IX_Messages_Status_SentAt ON Messages(Status, SentAt DESC);

-- Индексы для таблицы OutgoingMessages
CREATE INDEX IX_OutgoingMessages_Status ON OutgoingMessages(Status);
CREATE INDEX IX_OutgoingMessages_ScheduledAt ON OutgoingMessages(ScheduledAt);
CREATE INDEX IX_OutgoingMessages_ToUser ON OutgoingMessages(ToUser);
CREATE INDEX IX_OutgoingMessages_Priority ON OutgoingMessages(Priority DESC);
CREATE INDEX IX_OutgoingMessages_CreatedAt ON OutgoingMessages(CreatedAt DESC);

-- Составные индексы для очереди сообщений
CREATE INDEX IX_OutgoingMessages_Status_Priority ON OutgoingMessages(Status, Priority DESC, ScheduledAt);

-- Индексы для таблицы Contacts
CREATE INDEX IX_Contacts_PhoneNumber ON Contacts(PhoneNumber);
CREATE INDEX IX_Contacts_LastMessageAt ON Contacts(LastMessageAt DESC);
CREATE INDEX IX_Contacts_IsActive ON Contacts(IsActive);
CREATE INDEX IX_Contacts_IsBlocked ON Contacts(IsBlocked);
CREATE INDEX IX_Contacts_ContactName ON Contacts(ContactName);
CREATE INDEX IX_Contacts_UpdatedAt ON Contacts(UpdatedAt DESC);

-- Индексы для таблицы Settings
CREATE INDEX IX_Settings_Category ON Settings(Category);
CREATE INDEX IX_Settings_UpdatedAt ON Settings(UpdatedAt DESC);

-- Индексы для таблицы Logs
CREATE INDEX IX_Logs_CreatedAt ON Logs(CreatedAt DESC);
CREATE INDEX IX_Logs_LogLevel ON Logs(LogLevel);
CREATE INDEX IX_Logs_Source ON Logs(Source);
CREATE INDEX IX_Logs_UserId ON Logs(UserId);

-- Составные индексы для логов
CREATE INDEX IX_Logs_Level_CreatedAt ON Logs(LogLevel, CreatedAt DESC);
CREATE INDEX IX_Logs_Source_CreatedAt ON Logs(Source, CreatedAt DESC);

-- =====================================================
-- Представления для удобства работы с данными
-- =====================================================

-- Представление для просмотра последних сообщений с расширенной информацией
CREATE VIEW vw_RecentMessages AS
SELECT 
    m.Id,
    m.MessageID,
    m.ChatID,
    m.FromUser,
    c.ContactName,
    c.Company,
    m.Text,
    m.MessageType,
    m.Direction,
    m.SentAt,
    m.ReceivedAt,
    m.Status,
    CASE 
        WHEN m.Direction = 'incoming' THEN 'Входящее'
        WHEN m.Direction = 'outgoing' THEN 'Исходящее'
        ELSE 'Неизвестно'
    END AS DirectionText
FROM Messages m
LEFT JOIN Contacts c ON m.FromUser = c.PhoneNumber;
GO

-- Представление для непрочитанных входящих сообщений
CREATE VIEW vw_UnreadMessages AS
SELECT 
    m.Id,
    m.MessageID,
    m.ChatID,
    m.FromUser,
    c.ContactName,
    c.Company,
    m.Text,
    m.MessageType,
    m.SentAt,
    m.ReceivedAt,
    DATEDIFF(MINUTE, m.ReceivedAt, GETDATE()) AS MinutesAgo
FROM Messages m
LEFT JOIN Contacts c ON m.FromUser = c.PhoneNumber
WHERE m.Status = 'received' AND m.Direction = 'incoming';
GO

-- Представление для статистики по контактам с дополнительными метриками
CREATE VIEW vw_ContactStats AS
SELECT 
    c.Id,
    c.PhoneNumber,
    c.ContactName,
    c.Company,
    c.Email,
    c.Tags,
    c.MessageCount,
    c.LastMessageAt,
    c.IsActive,
    c.IsBlocked,
    DATEDIFF(DAY, c.LastMessageAt, GETDATE()) AS DaysSinceLastMessage,
    CASE 
        WHEN c.LastMessageAt IS NULL THEN 'Нет сообщений'
        WHEN DATEDIFF(DAY, c.LastMessageAt, GETDATE()) = 0 THEN 'Сегодня'
        WHEN DATEDIFF(DAY, c.LastMessageAt, GETDATE()) = 1 THEN 'Вчера'
        WHEN DATEDIFF(DAY, c.LastMessageAt, GETDATE()) <= 7 THEN 'На этой неделе'
        WHEN DATEDIFF(DAY, c.LastMessageAt, GETDATE()) <= 30 THEN 'В этом месяце'
        ELSE 'Давно'
    END AS LastMessagePeriod
FROM Contacts c;
GO

-- Представление для очереди исходящих сообщений
CREATE VIEW vw_OutgoingQueue AS
SELECT 
    om.Id,
    om.ToUser,
    c.ContactName,
    om.MessageText,
    om.MessageType,
    om.Priority,
    om.ScheduledAt,
    om.CreatedAt,
    om.Status,
    om.RetryCount,
    om.ErrorMessage,
    CASE 
        WHEN om.Status = 'pending' AND (om.ScheduledAt IS NULL OR om.ScheduledAt <= GETDATE()) THEN 'Готово к отправке'
        WHEN om.Status = 'pending' AND om.ScheduledAt > GETDATE() THEN 'Запланировано'
        WHEN om.Status = 'sent' THEN 'Отправлено'
        WHEN om.Status = 'failed' THEN 'Ошибка'
        WHEN om.Status = 'cancelled' THEN 'Отменено'
        ELSE om.Status
    END AS StatusText
FROM OutgoingMessages om
LEFT JOIN Contacts c ON om.ToUser = c.PhoneNumber;
GO

-- Представление для анализа активности по дням
CREATE VIEW vw_DailyActivity AS
SELECT 
    CAST(m.ReceivedAt AS DATE) AS MessageDate,
    COUNT(*) AS TotalMessages,
    COUNT(CASE WHEN m.Direction = 'incoming' THEN 1 END) AS IncomingMessages,
    COUNT(CASE WHEN m.Direction = 'outgoing' THEN 1 END) AS OutgoingMessages,
    COUNT(DISTINCT m.FromUser) AS UniqueContacts
FROM Messages m
WHERE m.ReceivedAt >= DATEADD(DAY, -30, GETDATE())
GROUP BY CAST(m.ReceivedAt AS DATE);
GO

-- Представление для топ активных контактов
CREATE VIEW vw_TopActiveContacts AS
SELECT TOP 50
    c.PhoneNumber,
    c.ContactName,
    c.Company,
    c.MessageCount,
    c.LastMessageAt,
    COUNT(m.Id) AS RecentMessages,
    MAX(m.ReceivedAt) AS LastActivity
FROM Contacts c
LEFT JOIN Messages m ON c.PhoneNumber = m.FromUser 
    AND m.ReceivedAt >= DATEADD(DAY, -7, GETDATE())
WHERE c.IsActive = 1 AND c.IsBlocked = 0
GROUP BY c.PhoneNumber, c.ContactName, c.Company, c.MessageCount, c.LastMessageAt
ORDER BY c.MessageCount DESC, c.LastMessageAt DESC;
GO

-- Представление для системных логов с фильтрацией
CREATE VIEW vw_SystemLogs AS
SELECT 
    l.Id,
    l.LogLevel,
    l.Message,
    l.Source,
    l.UserId,
    l.RequestId,
    l.CreatedAt,
    CASE 
        WHEN l.LogLevel = 'DEBUG' THEN 'Отладка'
        WHEN l.LogLevel = 'INFO' THEN 'Информация'
        WHEN l.LogLevel = 'WARNING' THEN 'Предупреждение'
        WHEN l.LogLevel = 'ERROR' THEN 'Ошибка'
        WHEN l.LogLevel = 'CRITICAL' THEN 'Критическая ошибка'
        ELSE l.LogLevel
    END AS LogLevelText
FROM Logs l
WHERE l.CreatedAt >= DATEADD(DAY, -7, GETDATE());
GO

-- =====================================================
-- Хранимые процедуры для обслуживания
-- =====================================================

-- Процедура для очистки старых логов
CREATE PROCEDURE sp_CleanupOldLogs
    @RetentionDays INT = 30
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CutoffDate DATETIME = DATEADD(DAY, -@RetentionDays, GETDATE());
    
    DELETE FROM Logs 
    WHERE CreatedAt < @CutoffDate;
    
    SELECT @@ROWCOUNT AS DeletedRows;
END;
GO

-- Процедура для обновления статистики контактов
CREATE PROCEDURE sp_UpdateContactStats
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE c
    SET 
        MessageCount = ISNULL(stats.MessageCount, 0),
        LastMessageAt = stats.LastMessageAt,
        UpdatedAt = GETDATE()
    FROM Contacts c
    LEFT JOIN (
        SELECT 
            FromUser,
            COUNT(*) AS MessageCount,
            MAX(SentAt) AS LastMessageAt
        FROM Messages 
        WHERE Direction = 'incoming'
        GROUP BY FromUser
    ) stats ON c.PhoneNumber = stats.FromUser;
    
    SELECT @@ROWCOUNT AS UpdatedContacts;
END;
GO